# Flink Checkpoint Inspector

A command-line tool for inspecting and analyzing Apache Flink checkpoints and savepoints.

## Overview

The Flink Checkpoint Inspector provides a comprehensive set of commands to examine the structure and contents of Flink checkpoints. It allows you to:

- View checkpoint metadata and operator information
- Inspect keyed state and operator state
- Analyze key group distribution
- Dump state data to files for further analysis
- Understand checkpoint structure and troubleshoot state-related issues

## Building

Build the tool using <PERSON>ven:

```bash
cd flink-checkpoint-inspector
mvn clean package
```

This will create a fat JAR with all dependencies in the `target/` directory.

## Usage

The tool provides several commands for different types of analysis:

### Basic Syntax

```bash
java -jar target/flink-checkpoint-inspector-*.jar <command> <checkpoint-path> [options]
```

### Commands

#### 1. Metadata - Show checkpoint metadata

```bash
java -jar flink-checkpoint-inspector.jar metadata /path/to/checkpoint
```

Displays:
- Checkpoint ID and properties
- Master states
- Operator summary (count, total state size)
- Detailed operator information

#### 2. Operators - List all operators and their state

```bash
java -jar flink-checkpoint-inspector.jar operators /path/to/checkpoint
```

Shows:
- All operators with their IDs and parallelism
- Subtask state breakdown
- State types and sizes

#### 3. Keyed State - Inspect keyed state for specific operator

```bash
java -jar flink-checkpoint-inspector.jar keyed-state /path/to/checkpoint --operator-id <operator-id> [--subtask <index>] [--verbose]
```

Options:
- `--operator-id`: Required. Operator ID (hex string or UUID)
- `--subtask`: Subtask index (default: 0)
- `--verbose`: Show detailed state information

#### 4. Operator State - Inspect operator state for specific operator

```bash
java -jar flink-checkpoint-inspector.jar operator-state /path/to/checkpoint --operator-id <operator-id> [--subtask <index>] [--verbose]
```

Similar options to keyed-state command.

#### 5. Dump State - Export state data to file

```bash
java -jar flink-checkpoint-inspector.jar dump-state /path/to/checkpoint --operator-id <operator-id> [options]
```

Options:
- `--operator-id`: Required. Operator ID
- `--subtask`: Subtask index (default: 0)
- `--state-name`: Specific state name to dump
- `--output`: Output file path (default: stdout)
- `--format`: Output format - json or csv (default: json)
- `--limit`: Limit number of records

#### 6. Key Groups - Analyze key group distribution

```bash
java -jar flink-checkpoint-inspector.jar key-groups /path/to/checkpoint
```

Shows:
- Key group ranges for each operator
- Distribution analysis
- Missing or overlapping key groups
- Summary statistics

## Examples

### Basic checkpoint inspection

```bash
# Show checkpoint metadata
java -jar flink-checkpoint-inspector.jar metadata /tmp/checkpoint-123

# List all operators
java -jar flink-checkpoint-inspector.jar operators /tmp/checkpoint-123
```

### Detailed state analysis

```bash
# Inspect keyed state for specific operator
java -jar flink-checkpoint-inspector.jar keyed-state /tmp/checkpoint-123 \
  --operator-id a1b2c3d4-e5f6-7890-abcd-ef1234567890 \
  --subtask 0 \
  --verbose

# Analyze key group distribution
java -jar flink-checkpoint-inspector.jar key-groups /tmp/checkpoint-123
```

### Export state data

```bash
# Dump all state data to JSON
java -jar flink-checkpoint-inspector.jar dump-state /tmp/checkpoint-123 \
  --operator-id a1b2c3d4-e5f6-7890-abcd-ef1234567890 \
  --output state-dump.json \
  --format json

# Export specific state to CSV with limit
java -jar flink-checkpoint-inspector.jar dump-state /tmp/checkpoint-123 \
  --operator-id a1b2c3d4-e5f6-7890-abcd-ef1234567890 \
  --state-name "my-value-state" \
  --output state-data.csv \
  --format csv \
  --limit 1000
```

## Architecture

The tool is structured as follows:

- **Main Class**: `FlinkCheckpointInspector` - Entry point and command routing
- **Commands**: Individual command implementations in `commands/` package
- **Utilities**: Helper classes in `utils/` package for metadata and state data processing

### Key Components

1. **CheckpointMetadataUtils**: Utilities for working with checkpoint metadata
2. **StateDataReader**: Low-level state data reading and deserialization
3. **Command Classes**: Individual implementations for each command type

## Limitations

- State data deserialization requires proper type serializers, which may not always be available
- Some state formats may not be fully supported depending on the Flink version
- Large checkpoints may require significant memory for processing

## Dependencies

The tool depends on:
- Flink Runtime (`flink-runtime`)
- Flink State Processing API (`flink-state-processing-api`)
- Picocli for command-line parsing
- Jackson for JSON output

## Troubleshooting

### Common Issues

1. **"Operator not found"**: Check that the operator ID is correct (use `operators` command first)
2. **"Subtask not found"**: Verify the subtask index exists for the operator
3. **State deserialization errors**: May occur if type serializers are not available

### Getting Help

Use the help command to see all available options:

```bash
java -jar flink-checkpoint-inspector.jar help
```

## Contributing

This tool is part of the Apache Flink project. Contributions should follow the standard Flink development process.
