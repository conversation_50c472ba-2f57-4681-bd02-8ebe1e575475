/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.tools.checkpoint.commands;

import org.apache.flink.core.fs.FSDataInputStream;
import org.apache.flink.core.memory.DataInputViewStreamWrapper;
import org.apache.flink.runtime.checkpoint.OperatorState;
import org.apache.flink.runtime.checkpoint.OperatorSubtaskState;
import org.apache.flink.runtime.checkpoint.metadata.CheckpointMetadata;
import org.apache.flink.runtime.state.KeyGroupRange;
import org.apache.flink.runtime.state.KeyGroupsStateHandle;
import org.apache.flink.runtime.state.KeyedStateHandle;
import org.apache.flink.runtime.state.metainfo.StateMetaInfoSnapshot;
import org.apache.flink.runtime.state.KeyedBackendSerializationProxy;
import org.apache.flink.tools.checkpoint.FlinkCheckpointInspector;
import org.apache.flink.tools.checkpoint.utils.CheckpointMetadataUtils;
import org.apache.flink.tools.checkpoint.utils.StateDataReader;
import picocli.CommandLine;

import java.util.List;

/**
 * Command to inspect keyed state for a specific operator.
 */
@CommandLine.Command(name = "keyed-state", description = "Inspect keyed state for specific operator")
public class KeyedStateCommand implements Command {

    @CommandLine.Parameters(index = "0", description = "The path to the checkpoint.")
    private String checkpointPath;

    @CommandLine.Option(names = {"-o", "--operator-id"}, required = true, description = "Operator ID")
    private String operatorId;

    @CommandLine.Option(names = {"-s", "--subtask"}, description = "Subtask index (default: 0)", defaultValue = "0")
    private int subtaskIndex;

    @CommandLine.Option(names = {"-v", "--verbose"}, description = "Verbose output")
    private boolean verbose;

    @Override
    public Integer call() throws Exception {
        CheckpointMetadata metadata = FlinkCheckpointInspector.loadMetadata(checkpointPath);
        OperatorState operatorState = FlinkCheckpointInspector.findOperatorById(metadata, operatorId);

        if (operatorState == null) {
            System.err.println("Operator not found: " + operatorId);
            return 1;
        }

        inspectKeyedState(operatorState, subtaskIndex, verbose);
        return 0;
    }

    private void inspectKeyedState(OperatorState operatorState, int subtaskIndex, boolean verbose) throws Exception {
        System.out.println("=== Keyed State Inspection ===");
        System.out.println("Operator ID: " + operatorState.getOperatorID());
        System.out.println("Subtask: " + subtaskIndex);
        System.out.println();

        OperatorSubtaskState subtaskState = operatorState.getSubtaskStates().get(subtaskIndex);
        if (subtaskState == null) {
            System.err.println("Subtask not found: " + subtaskIndex);
            return;
        }

        // Inspect managed keyed state
        if (!subtaskState.getManagedKeyedState().isEmpty()) {
            System.out.println("Managed Keyed State:");
            for (KeyedStateHandle handle : subtaskState.getManagedKeyedState()) {
                inspectKeyedStateHandle(handle, verbose);
            }
        }

        // Inspect raw keyed state
        if (!subtaskState.getRawKeyedState().isEmpty()) {
            System.out.println("Raw Keyed State:");
            for (KeyedStateHandle handle : subtaskState.getRawKeyedState()) {
                inspectKeyedStateHandle(handle, verbose);
            }
        }

        if (subtaskState.getManagedKeyedState().isEmpty() && subtaskState.getRawKeyedState().isEmpty()) {
            System.out.println("No keyed state found for this subtask.");
        }
    }

    private void inspectKeyedStateHandle(KeyedStateHandle handle, boolean verbose) throws Exception {
        System.out.println("  Handle Type: " + handle.getClass().getSimpleName());
        System.out.println("  State Size: " + CheckpointMetadataUtils.formatBytes(handle.getStateSize()));
        System.out.println("  Key Group Range: " + handle.getKeyGroupRange());
        System.out.println("  State Handle ID: " + handle.getStateHandleId());

        if (handle instanceof KeyGroupsStateHandle && verbose) {
            KeyGroupsStateHandle keyGroupsHandle = (KeyGroupsStateHandle) handle;
            inspectKeyGroupsStateHandle(keyGroupsHandle);
        }

        System.out.println();
    }

    private void inspectKeyGroupsStateHandle(KeyGroupsStateHandle handle) throws Exception {
        System.out.println("  Detailed Key Groups State:");
        
        KeyGroupRange keyGroupRange = handle.getKeyGroupRange();
        System.out.println("    Key Groups: " + keyGroupRange.getStartKeyGroup() + 
                          " to " + keyGroupRange.getEndKeyGroup() + 
                          " (total: " + keyGroupRange.getNumberOfKeyGroups() + ")");

        // Show offsets for each key group
        System.out.println("    Key Group Offsets:");
        for (int keyGroup : keyGroupRange) {
            long offset = handle.getOffsetForKeyGroup(keyGroup);
            System.out.println("      Key Group " + keyGroup + ": offset " + offset);
        }

        // Try to read state metadata
        try (FSDataInputStream inputStream = handle.openInputStream()) {
            DataInputViewStreamWrapper inView = new DataInputViewStreamWrapper(inputStream);
            
            KeyedBackendSerializationProxy<?> serializationProxy = 
                new KeyedBackendSerializationProxy<>(getClass().getClassLoader());
            serializationProxy.read(inView);
            
            System.out.println("    State Metadata:");
            System.out.println("      Compression: " + serializationProxy.isUsingKeyGroupCompression());
            
            List<StateMetaInfoSnapshot> stateMetaInfos = serializationProxy.getStateMetaInfoSnapshots();
            System.out.println("      States (" + stateMetaInfos.size() + "):");
            
            for (int i = 0; i < stateMetaInfos.size(); i++) {
                StateMetaInfoSnapshot metaInfo = stateMetaInfos.get(i);
                System.out.println("        " + i + ": " + metaInfo.getName());
                System.out.println("           Type: " + metaInfo.getBackendStateType());
                System.out.println("           Options: " + metaInfo.getOptions());
            }
            
        } catch (Exception e) {
            System.out.println("    Error reading state metadata: " + e.getMessage());
        }
    }
}
