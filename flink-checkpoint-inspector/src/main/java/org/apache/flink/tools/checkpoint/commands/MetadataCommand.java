/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.tools.checkpoint.commands;

import org.apache.flink.runtime.checkpoint.MasterState;
import org.apache.flink.runtime.checkpoint.OperatorState;
import org.apache.flink.runtime.checkpoint.metadata.CheckpointMetadata;
import org.apache.flink.tools.checkpoint.FlinkCheckpointInspector;
import org.apache.flink.tools.checkpoint.utils.CheckpointMetadataUtils;
import picocli.CommandLine;

@CommandLine.Command(name = "metadata", description = "Show checkpoint metadata")
public class MetadataCommand implements Command {

    @CommandLine.Parameters(index = "0", description = "The path to the checkpoint.")
    private String checkpointPath;

    @Override
    public Integer call() throws Exception {
        CheckpointMetadata metadata = FlinkCheckpointInspector.loadMetadata(checkpointPath);
        printMetadata(metadata);
        return 0;
    }

    private void printMetadata(CheckpointMetadata metadata) {
        System.out.println("=== Checkpoint Metadata ===");
        System.out.println("Checkpoint ID: " + metadata.getCheckpointId());
        System.out.println();

        // Master states
        if (!metadata.getMasterStates().isEmpty()) {
            System.out.println("Master States:");
            for (MasterState masterState : metadata.getMasterStates()) {
                System.out.println("  - Name: " + masterState.name());
                System.out.println("    Version: " + masterState.version());
                System.out.println(
                        "    Size: "
                                + CheckpointMetadataUtils.formatBytes(masterState.bytes().length));
            }
            System.out.println();
        }

        // Operator states summary
        System.out.println("Operator States:");
        System.out.println("  Total Operators: " + metadata.getOperatorStates().size());

        long totalStateSize = 0;
        int totalSubtasks = 0;

        for (OperatorState operatorState : metadata.getOperatorStates()) {
            totalSubtasks += operatorState.getSubtaskStates().size();
            totalStateSize += operatorState.getStateSize();
        }

        System.out.println("  Total Subtasks: " + totalSubtasks);
        System.out.println(
                "  Total State Size: " + CheckpointMetadataUtils.formatBytes(totalStateSize));
        System.out.println();

        // Detailed operator information
        System.out.println("Operator Details:");
        for (OperatorState operatorState : metadata.getOperatorStates()) {
            System.out.println("  Operator ID: " + operatorState.getOperatorID());
            System.out.println("    Parallelism: " + operatorState.getParallelism());
            System.out.println("    Max Parallelism: " + operatorState.getMaxParallelism());
            System.out.println("    Subtasks: " + operatorState.getSubtaskStates().size());
            System.out.println(
                    "    State Size: "
                            + CheckpointMetadataUtils.formatBytes(operatorState.getStateSize()));
            System.out.println("    Fully Finished: " + operatorState.isFullyFinished());

            if (operatorState.getCoordinatorState() != null) {
                System.out.println(
                        "    Coordinator State Size: "
                                + CheckpointMetadataUtils.formatBytes(
                                        operatorState.getCoordinatorState().getStateSize()));
            }
            System.out.println();
        }

        // Checkpoint properties
        if (metadata.getProperties() != null) {
            System.out.println("Checkpoint Properties:");
            System.out.println(
                    "  Checkpoint Type: " + metadata.getProperties().getCheckpointType());
            System.out.println(
                    "  Savepoint Format: " + metadata.getProperties().getSavepointFormat());
            System.out.println(
                    "  Discard Subsumed: "
                            + metadata.getProperties().discardSubsumedCheckpoints());
        }
    }
}
