/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.tools.checkpoint;

import org.apache.flink.runtime.checkpoint.OperatorState;
import org.apache.flink.runtime.checkpoint.OperatorSubtaskState;
import org.apache.flink.runtime.checkpoint.metadata.CheckpointMetadata;
import org.apache.flink.runtime.jobgraph.OperatorID;
import org.apache.flink.runtime.state.KeyedStateHandle;
import org.apache.flink.runtime.state.OperatorStateHandle;
import org.apache.flink.tools.checkpoint.utils.CheckpointMetadataUtils;

import org.junit.jupiter.api.Test;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Tests for the Flink Checkpoint Inspector.
 */
public class FlinkCheckpointInspectorTest {

    @Test
    public void testFindOperatorById() {
        // Create a mock operator state
        OperatorID operatorId = new OperatorID();
        OperatorState operatorState = new OperatorState(operatorId, 1, 128);
        
        // Create mock checkpoint metadata
        CheckpointMetadata metadata = new CheckpointMetadata(
            12345L,
            Collections.singletonList(operatorState),
            Collections.emptyList()
        );

        // Test finding by full UUID string
        OperatorState found = FlinkCheckpointInspector.findOperatorById(metadata, operatorId.toString());
        assertThat(found).isNotNull();
        assertThat(found.getOperatorID()).isEqualTo(operatorId);

        // Test finding by hex string
        OperatorState foundByHex = FlinkCheckpointInspector.findOperatorById(metadata, operatorId.toHexString());
        assertThat(foundByHex).isNotNull();
        assertThat(foundByHex.getOperatorID()).isEqualTo(operatorId);

        // Test not found
        OperatorState notFound = FlinkCheckpointInspector.findOperatorById(metadata, "nonexistent");
        assertThat(notFound).isNull();
    }

    @Test
    public void testCheckpointMetadataUtils() {
        // Create mock operator states
        OperatorID operatorId1 = new OperatorID();
        OperatorID operatorId2 = new OperatorID();
        
        OperatorState operatorWithKeyedState = new OperatorState(operatorId1, 1, 128);
        OperatorState operatorWithOperatorState = new OperatorState(operatorId2, 1, 128);
        
        // Add mock subtask states
        Map<Integer, OperatorSubtaskState> subtaskStates1 = new HashMap<>();
        OperatorSubtaskState subtaskState1 = OperatorSubtaskState.builder()
            .setManagedKeyedState(Collections.singletonList(createMockKeyedStateHandle()))
            .build();
        subtaskStates1.put(0, subtaskState1);
        operatorWithKeyedState.putState(0, subtaskState1);
        
        Map<Integer, OperatorSubtaskState> subtaskStates2 = new HashMap<>();
        OperatorSubtaskState subtaskState2 = OperatorSubtaskState.builder()
            .setManagedOperatorState(Collections.singletonList(createMockOperatorStateHandle()))
            .build();
        subtaskStates2.put(0, subtaskState2);
        operatorWithOperatorState.putState(0, subtaskState2);

        CheckpointMetadata metadata = new CheckpointMetadata(
            12345L,
            List.of(operatorWithKeyedState, operatorWithOperatorState),
            Collections.emptyList()
        );

        // Test utility methods
        List<OperatorState> keyedStateOps = CheckpointMetadataUtils.getOperatorsWithKeyedState(metadata);
        assertThat(keyedStateOps).hasSize(1);
        assertThat(keyedStateOps.get(0).getOperatorID()).isEqualTo(operatorId1);

        List<OperatorState> operatorStateOps = CheckpointMetadataUtils.getOperatorsWithOperatorState(metadata);
        assertThat(operatorStateOps).hasSize(1);
        assertThat(operatorStateOps.get(0).getOperatorID()).isEqualTo(operatorId2);

        assertThat(CheckpointMetadataUtils.hasKeyedState(operatorWithKeyedState)).isTrue();
        assertThat(CheckpointMetadataUtils.hasKeyedState(operatorWithOperatorState)).isFalse();

        assertThat(CheckpointMetadataUtils.hasOperatorState(operatorWithOperatorState)).isTrue();
        assertThat(CheckpointMetadataUtils.hasOperatorState(operatorWithKeyedState)).isFalse();
    }

    @Test
    public void testCheckpointSummary() {
        OperatorID operatorId = new OperatorID();
        OperatorState operatorState = new OperatorState(operatorId, 2, 128);
        
        // Add subtask states
        OperatorSubtaskState subtaskState1 = OperatorSubtaskState.builder()
            .setManagedKeyedState(Collections.singletonList(createMockKeyedStateHandle()))
            .build();
        OperatorSubtaskState subtaskState2 = OperatorSubtaskState.builder()
            .setManagedKeyedState(Collections.singletonList(createMockKeyedStateHandle()))
            .build();
        
        operatorState.putState(0, subtaskState1);
        operatorState.putState(1, subtaskState2);

        CheckpointMetadata metadata = new CheckpointMetadata(
            12345L,
            Collections.singletonList(operatorState),
            Collections.emptyList()
        );

        CheckpointMetadataUtils.CheckpointSummary summary = 
            CheckpointMetadataUtils.getCheckpointSummary(metadata);

        assertThat(summary.checkpointId).isEqualTo(12345L);
        assertThat(summary.operatorCount).isEqualTo(1);
        assertThat(summary.totalSubtasks).isEqualTo(2);
        assertThat(summary.operatorsWithKeyedState).isEqualTo(1);
        assertThat(summary.operatorsWithOperatorState).isEqualTo(0);
        assertThat(summary.masterStateCount).isEqualTo(0);
    }

    private KeyedStateHandle createMockKeyedStateHandle() {
        return new KeyedStateHandle() {
            @Override
            public org.apache.flink.runtime.state.KeyGroupRange getKeyGroupRange() {
                return org.apache.flink.runtime.state.KeyGroupRange.of(0, 127);
            }

            @Override
            public org.apache.flink.runtime.state.StateHandleID getStateHandleId() {
                return new org.apache.flink.runtime.state.StateHandleID("mock-handle");
            }

            @Override
            public long getStateSize() {
                return 1024L;
            }

            @Override
            public void discardState() throws Exception {
                // Mock implementation
            }
        };
    }

    private OperatorStateHandle createMockOperatorStateHandle() {
        return new OperatorStateHandle() {
            @Override
            public Map<String, StateMetaInfo> getStateNameToPartitionOffsets() {
                return Collections.singletonMap("test-state", 
                    new StateMetaInfo(new long[]{0}, OperatorStateHandle.Mode.SPLIT_DISTRIBUTE));
            }

            @Override
            public org.apache.flink.runtime.state.StateHandleID getStateHandleId() {
                return new org.apache.flink.runtime.state.StateHandleID("mock-operator-handle");
            }

            @Override
            public long getStateSize() {
                return 512L;
            }

            @Override
            public void discardState() throws Exception {
                // Mock implementation
            }

            @Override
            public org.apache.flink.core.fs.FSDataInputStream openInputStream() throws java.io.IOException {
                throw new UnsupportedOperationException("Mock implementation");
            }
        };
    }
}
